#!/usr/bin/env python3
"""
Test script to verify the enhanced follow-up question strategy with current story tracking.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.conversational_engine import ConversationalEngine
from core.conversation_manager import ConversationManager
from core.models import generate_terminal_chat_id
import json

def test_enhanced_follow_up_strategy():
    """Test the enhanced follow-up question strategy with current story tracking."""
    
    # Use a test bot ID (you can replace with actual bot ID)
    test_bot_id = "1cf7f2c9-f132-40e2-922f-634dfafa9605"  # Rickshaw coffee bot
    
    try:
        # Initialize the conversational engine
        engine = ConversationalEngine(test_bot_id)
        
        # Test messages
        test_messages = [
            "Tell me about your experiences with coffee",
            "What challenges did you face with your business?",
            "How did you overcome those challenges?"
        ]
        
        print("Testing enhanced follow-up question strategy...")
        print("=" * 60)
        
        for i, test_message in enumerate(test_messages, 1):
            print(f"\nTest {i}: {test_message}")
            print("-" * 50)
            
            # Generate response
            response = engine.generate_response(
                user_message=test_message,
                bot_id=test_bot_id
            )
            
            print("Response:")
            print(response.response)
            print("\nFollow-up questions:")
            
            for j, question in enumerate(response.follow_up_questions, 1):
                strategy_type = ""
                if j == 1:
                    strategy_type = " (Current Story Deep Dive)"
                elif j == 2:
                    strategy_type = " (Explore Other Stories - Random Selection)"
                elif j == 3:
                    strategy_type = " (LLM Choice)"
                
                print(f"{j}. {question}{strategy_type}")
            
            # Verify we have exactly 3 questions
            if len(response.follow_up_questions) == 3:
                print("✓ Correct number of follow-up questions (3)")
            else:
                print(f"✗ Expected 3 follow-up questions, got {len(response.follow_up_questions)}")
        
        print("\n" + "=" * 60)
        print("Enhanced follow-up strategy test completed successfully!")
        
        # Test current story tracking
        print("\nTesting current story tracking...")
        chat_id = generate_terminal_chat_id(test_bot_id)
        conversation_manager = ConversationManager(chat_id, test_bot_id)
        
        if conversation_manager.current_story_id:
            print(f"✓ Current story ID tracked: {conversation_manager.current_story_id}")
        else:
            print("ℹ No current story set (this is normal for new conversations)")
        
        return True
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_follow_up_strategy()
    sys.exit(0 if success else 1)
